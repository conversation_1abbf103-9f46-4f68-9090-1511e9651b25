import { useState, useEffect } from "react"import cssText from "data-text:~style.css"

import cssText from "data-text:~style.css"import type { PlasmoCSConfig } from "plasmo"

import type { PlasmoCSConfig } from "plasmo"import { useEffect, useState } from "react"



export const config: PlasmoCSConfig = {export const config: PlasmoCSConfig = {

  matches: ["<all_urls>"]  matches: ["<all_urls>"]

}}



export const getStyle = (): HTMLStyleElement => {export const getStyle = (): HTMLStyleElement => {

  const baseFontSize = 16  const baseFontSize = 16

  let updatedCssText = cssText.replaceAll(":root", ":host(plasmo-csui)")

  const remRegex = /([\d.]+)rem/g  let updatedCssText = cssText.replaceAll(":root", ":host(plasmo-csui)")

  updatedCssText = updatedCssText.replace(remRegex, (match, remValue) => {  const remRegex = /([\d.]+)rem/g

    const pixelsValue = parseFloat(remValue) * baseFontSize  updatedCssText = updatedCssText.replace(remRegex, (match, remValue) => {

    return `${pixelsValue}px`    const pixelsValue = parseFloat(remValue) * baseFontSize

  })    return `${pixelsValue}px`

  })

  const styleElement = document.createElement("style")

  styleElement.textContent = updatedCssText  const styleElement = document.createElement("style")

  return styleElement  styleElement.textContent = updatedCssText

}  return styleElement

}

const TranslationTooltip = () => {

  const [tooltip, setTooltip] = useState<{interface TooltipState {

    visible: boolean  visible: boolean

    x: number  x: number

    y: number  y: number

    text: string  selectedText: string

    translation: string  translatedText: string

  }>({  isLoading: boolean

    visible: false,}

    x: 0,

    y: 0,const TranslationTooltip = () => {

    text: "",  const [tooltipState, setTooltipState] = useState<TooltipState>({

    translation: ""    visible: false,

  })    x: 0,

    y: 0,

  const handleSelection = () => {    selectedText: "",

    const selection = window.getSelection()    translatedText: "",

    const selectedText = selection?.toString().trim()    isLoading: false

  })

    if (selectedText && selectedText.length > 1) {

      const range = selection?.getRangeAt(0)  const mockTranslate = async (text: string) => {

      const rect = range?.getBoundingClientRect()    await new Promise((resolve) => setTimeout(resolve, 800))

    return `Dịch: ${text}`

      if (rect) {  }

        setTooltip({

          visible: true,  const handleTextSelection = async () => {

          x: rect.left + window.scrollX + (rect.width / 2),    const selection = window.getSelection()

          y: rect.top + window.scrollY - 10,    const selectedText = selection?.toString().trim()

          text: selectedText,

          translation: `Dịch: ${selectedText}`    if (selectedText && selectedText.length > 1) {

        })      const range = selection?.getRangeAt(0)

      }      const rect = range?.getBoundingClientRect()

    } else {

      setTooltip(prev => ({ ...prev, visible: false }))      if (rect) {

    }        setTooltipState({

  }          visible: true,

          x: rect.left + window.scrollX + rect.width / 2,

  useEffect(() => {          y: rect.top + window.scrollY - 10,

    document.addEventListener("mouseup", handleSelection)          selectedText,

    document.addEventListener("mousedown", () => {          translatedText: "",

      setTooltip(prev => ({ ...prev, visible: false }))          isLoading: true

    })        })



    return () => {        try {

      document.removeEventListener("mouseup", handleSelection)          const translation = await mockTranslate(selectedText)

      document.removeEventListener("mousedown", () => {          setTooltipState((prev) => ({

        setTooltip(prev => ({ ...prev, visible: false }))            ...prev,

      })            translatedText: translation,

    }            isLoading: false

  }, [])          }))

        } catch (error) {

  if (!tooltip.visible) return null          setTooltipState((prev) => ({

            ...prev,

  return (            translatedText: "Lỗi khi dịch",

    <div            isLoading: false

      className="plasmo-fixed plasmo-z-[10000] plasmo-bg-white plasmo-shadow-lg plasmo-rounded-lg plasmo-p-3 plasmo-max-w-xs"          }))

      style={{        }

        left: `${tooltip.x}px`,      }

        top: `${tooltip.y}px`,    } else {

        transform: "translate(-50%, -100%)"      setTooltipState((prev) => ({ ...prev, visible: false }))

      }}    }

    >  }

      <div className="plasmo-text-sm plasmo-text-gray-800 plasmo-mb-2">{tooltip.text}</div>

      <div className="plasmo-text-sm plasmo-text-blue-600 plasmo-font-medium">{tooltip.translation}</div>  const copyToClipboard = (text: string) => {

    </div>    navigator.clipboard.writeText(text)

  )    setTooltipState((prev) => ({ ...prev, visible: false }))

}  }



export default TranslationTooltip  useEffect(() => {
    document.addEventListener("mouseup", handleTextSelection)
    document.addEventListener("mousedown", () => {
      setTooltipState((prev) => ({ ...prev, visible: false }))
    })

    return () => {
      document.removeEventListener("mouseup", handleTextSelection)
      document.removeEventListener("mousedown", () => {
        setTooltipState((prev) => ({ ...prev, visible: false }))
      })
    }
  }, [])

  if (!tooltipState.visible) return null

  return (
    <div
      className="plasmo-fixed plasmo-z-[10000] plasmo-bg-white plasmo-shadow-2xl plasmo-rounded-lg plasmo-border plasmo-p-3 plasmo-max-w-xs plasmo-min-w-48"
      style={{
        left: `${tooltipState.x}px`,
        top: `${tooltipState.y}px`,
        transform: "translate(-50%, -100%)"
      }}>
      {/* Header */}
      <div className="plasmo-flex plasmo-items-center plasmo-justify-between plasmo-mb-2">
        <div className="plasmo-flex plasmo-items-center plasmo-space-x-2">
          <div className="plasmo-w-3 plasmo-h-3 plasmo-bg-gradient-to-r plasmo-from-blue-500 plasmo-to-purple-600 plasmo-rounded-full"></div>
          <span className="plasmo-text-xs plasmo-font-medium plasmo-text-gray-700">
            DubinQ
          </span>
        </div>
        <button
          onClick={() =>
            setTooltipState((prev) => ({ ...prev, visible: false }))
          }
          className="plasmo-p-1 plasmo-rounded-full plasmo-bg-gray-100 hover:plasmo-bg-gray-200 plasmo-transition-colors">
          <svg
            className="plasmo-w-3 plasmo-h-3"
            fill="currentColor"
            viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {/* Original Text */}
      <div className="plasmo-mb-2">
        <div className="plasmo-text-xs plasmo-text-gray-500 plasmo-mb-1">
          Văn bản gốc:
        </div>
        <div className="plasmo-text-sm plasmo-text-gray-800 plasmo-bg-gray-50 plasmo-p-2 plasmo-rounded plasmo-max-h-20 plasmo-overflow-y-auto">
          {tooltipState.selectedText}
        </div>
      </div>

      {/* Translation */}
      <div className="plasmo-mb-3">
        <div className="plasmo-text-xs plasmo-text-gray-500 plasmo-mb-1">
          Bản dịch:
        </div>
        {tooltipState.isLoading ? (
          <div className="plasmo-flex plasmo-items-center plasmo-space-x-2 plasmo-p-2">
            <div className="plasmo-w-3 plasmo-h-3 plasmo-border-2 plasmo-border-blue-500 plasmo-border-t-transparent plasmo-rounded-full plasmo-animate-spin"></div>
            <span className="plasmo-text-sm plasmo-text-gray-500">
              Đang dịch...
            </span>
          </div>
        ) : (
          <div className="plasmo-text-sm plasmo-text-gray-800 plasmo-bg-blue-50 plasmo-p-2 plasmo-rounded plasmo-border plasmo-border-blue-200">
            {tooltipState.translatedText}
          </div>
        )}
      </div>

      {/* Actions */}
      {!tooltipState.isLoading && tooltipState.translatedText && (
        <div className="plasmo-flex plasmo-space-x-2">
          <button
            onClick={() => copyToClipboard(tooltipState.translatedText)}
            className="plasmo-flex-1 plasmo-bg-gradient-to-r plasmo-from-blue-500 plasmo-to-purple-600 plasmo-text-white plasmo-py-1 plasmo-px-3 plasmo-rounded plasmo-text-xs plasmo-font-medium plasmo-transition-all hover:plasmo-shadow-md">
            Sao chép
          </button>
          <button
            onClick={() => copyToClipboard(tooltipState.selectedText)}
            className="plasmo-flex-1 plasmo-bg-gray-100 plasmo-text-gray-700 plasmo-py-1 plasmo-px-3 plasmo-rounded plasmo-text-xs plasmo-font-medium plasmo-transition-colors hover:plasmo-bg-gray-200">
            Sao chép gốc
          </button>
        </div>
      )}

      {/* Arrow */}
      <div className="plasmo-absolute plasmo-top-full plasmo-left-1/2 plasmo-transform plasmo--translate-x-1/2">
        <div className="plasmo-w-0 plasmo-h-0 plasmo-border-l-4 plasmo-border-r-4 plasmo-border-t-4 plasmo-border-transparent plasmo-border-t-white"></div>
      </div>
    </div>
  )
}

export default TranslationTooltip
