import cssText from "data-text:~style.css"
import type { PlasmoCSConfig } from "plasmo"
import { useEffect, useState } from "react"

export const config: PlasmoCSConfig = {
  matches: ["<all_urls>"]
}

export const getStyle = (): HTMLStyleElement => {
  const baseFontSize = 16
  let updatedCssText = cssText.replaceAll(":root", ":host(plasmo-csui)")
  const remRegex = /([\d.]+)rem/g

  updatedCssText = updatedCssText.replace(remRegex, (match, remValue) => {
    const pixelsValue = parseFloat(remValue) * baseFontSize
    return `${pixelsValue}px`
  })

  const styleElement = document.createElement("style")
  styleElement.textContent = updatedCssText
  return styleElement
}

interface TooltipState {
  visible: boolean
  x: number
  y: number
  selectedText: string
  translatedText: string
  isLoading: boolean
}

const TranslationTooltip = () => {
  const [tooltipState, setTooltipState] = useState<TooltipState>({
    visible: false,
    x: 0,
    y: 0,
    selectedText: "",
    translatedText: "",
    isLoading: false
  })

  const mockTranslate = async (text: string) => {
    await new Promise((resolve) => setTimeout(resolve, 800))
    return `Translation: ${text}`
  }

  const handleTextSelection = async () => {
    const selection = window.getSelection()
    const selectedText = selection?.toString().trim()

    if (selectedText && selectedText.length > 1) {
      const range = selection?.getRangeAt(0)
      const rect = range?.getBoundingClientRect()

      if (rect) {
        setTooltipState({
          visible: true,
          x: rect.left + window.scrollX + rect.width / 2,
          y: rect.top + window.scrollY - 10,
          selectedText,
          translatedText: "",
          isLoading: true
        })

        try {
          const translation = await mockTranslate(selectedText)
          setTooltipState((prev) => ({
            ...prev,
            translatedText: translation,
            isLoading: false
          }))
        } catch (error) {
          setTooltipState((prev) => ({
            ...prev,
            translatedText: "Translation error",
            isLoading: false
          }))
        }
      }
    } else {
      setTooltipState((prev) => ({ ...prev, visible: false }))
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setTooltipState((prev) => ({ ...prev, visible: false }))
  }

  useEffect(() => {
    document.addEventListener("mouseup", handleTextSelection)
    document.addEventListener("mousedown", () => {
      setTooltipState((prev) => ({ ...prev, visible: false }))
    })

    return () => {
      document.removeEventListener("mouseup", handleTextSelection)
      document.removeEventListener("mousedown", () => {
        setTooltipState((prev) => ({ ...prev, visible: false }))
      })
    }
  }, [])

  if (!tooltipState.visible) return null

  return (
    <div
      className="plasmo-fixed plasmo-z-[10000] plasmo-bg-white plasmo-shadow-2xl plasmo-rounded-lg plasmo-border plasmo-p-3 plasmo-max-w-xs plasmo-min-w-48"
      style={{
        left: `${tooltipState.x}px`,
        top: `${tooltipState.y}px`,
        transform: "translate(-50%, -100%)"
      }}>
      {/* Header */}
      <div className="plasmo-flex plasmo-items-center plasmo-justify-between plasmo-mb-2">
        <div className="plasmo-flex plasmo-items-center plasmo-space-x-2">
          <div className="plasmo-w-3 plasmo-h-3 plasmo-bg-gradient-to-r plasmo-from-blue-500 plasmo-to-purple-600 plasmo-rounded-full"></div>
          <span className="plasmo-text-xs plasmo-font-medium plasmo-text-gray-700">
            DubinQ
          </span>
        </div>
        <button
          onClick={() =>
            setTooltipState((prev) => ({ ...prev, visible: false }))
          }
          className="plasmo-p-1 plasmo-rounded-full plasmo-bg-gray-100 hover:plasmo-bg-gray-200 plasmo-transition-colors">
          <svg
            className="plasmo-w-3 plasmo-h-3"
            fill="currentColor"
            viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {/* Original Text */}
      <div className="plasmo-mb-2">
        <div className="plasmo-text-xs plasmo-text-gray-500 plasmo-mb-1">
          Original Text:
        </div>
        <div className="plasmo-text-sm plasmo-text-gray-800 plasmo-bg-gray-50 plasmo-p-2 plasmo-rounded plasmo-max-h-20 plasmo-overflow-y-auto">
          {tooltipState.selectedText}
        </div>
      </div>

      {/* Translation */}
      <div className="plasmo-mb-3">
        <div className="plasmo-text-xs plasmo-text-gray-500 plasmo-mb-1">
          Translation:
        </div>
        {tooltipState.isLoading ? (
          <div className="plasmo-flex plasmo-items-center plasmo-space-x-2 plasmo-p-2">
            <div className="plasmo-w-3 plasmo-h-3 plasmo-border-2 plasmo-border-blue-500 plasmo-border-t-transparent plasmo-rounded-full plasmo-animate-spin"></div>
            <span className="plasmo-text-sm plasmo-text-gray-500">
              Translating...
            </span>
          </div>
        ) : (
          <div className="plasmo-text-sm plasmo-text-gray-800 plasmo-bg-blue-50 plasmo-p-2 plasmo-rounded plasmo-border plasmo-border-blue-200">
            {tooltipState.translatedText}
          </div>
        )}
      </div>

      {/* Actions */}
      {!tooltipState.isLoading && tooltipState.translatedText && (
        <div className="plasmo-flex plasmo-space-x-2">
          <button
            onClick={() => copyToClipboard(tooltipState.translatedText)}
            className="plasmo-flex-1 plasmo-bg-gradient-to-r plasmo-from-blue-500 plasmo-to-purple-600 plasmo-text-white plasmo-py-1 plasmo-px-3 plasmo-rounded plasmo-text-xs plasmo-font-medium plasmo-transition-all hover:plasmo-shadow-md">
            Copy
          </button>
          <button
            onClick={() => copyToClipboard(tooltipState.selectedText)}
            className="plasmo-flex-1 plasmo-bg-gray-100 plasmo-text-gray-700 plasmo-py-1 plasmo-px-3 plasmo-rounded plasmo-text-xs plasmo-font-medium plasmo-transition-colors hover:plasmo-bg-gray-200">
            Copy Original
          </button>
        </div>
      )}

      {/* Arrow */}
      <div className="plasmo-absolute plasmo-top-full plasmo-left-1/2 plasmo-transform plasmo--translate-x-1/2">
        <div className="plasmo-w-0 plasmo-h-0 plasmo-border-l-4 plasmo-border-r-4 plasmo-border-t-4 plasmo-border-transparent plasmo-border-t-white"></div>
      </div>
    </div>
  )
}

export default TranslationTooltip
