// Translation service using MyMemory API (free translation service)
interface TranslationResponse {
  responseData: {
    translatedText: string
    match: number
  }
  responseStatus: number
  responseDetails: string
}

interface LibreTranslateResponse {
  translatedText: string
}

export class TranslationService {
  private static readonly MYMEMORY_API =
    "https://api.mymemory.translated.net/get"
  private static readonly LIBRETRANSLATE_API =
    "https://libretranslate.de/translate"

  // Language mapping for MyMemory API
  private static readonly languageMap: Record<string, string> = {
    auto: "auto",
    en: "en",
    vi: "vi",
    zh: "zh-CN",
    ja: "ja",
    ko: "ko",
    fr: "fr",
    de: "de",
    es: "es",
    ru: "ru",
    it: "it",
    pt: "pt",
    ar: "ar",
    hi: "hi",
    th: "th"
  }

  static async translateWithMyMemory(
    text: string,
    fromLang: string,
    toLang: string
  ): Promise<string> {
    try {
      const sourceLang = this.languageMap[fromLang] || fromLang
      const targetLang = this.languageMap[toLang] || toLang

      const url = new URL(this.MYMEMORY_API)
      url.searchParams.append("q", text)
      url.searchParams.append("langpair", `${sourceLang}|${targetLang}`)

      const response = await fetch(url.toString())
      const data: TranslationResponse = await response.json()

      if (data.responseStatus === 200 && data.responseData) {
        return data.responseData.translatedText
      } else {
        throw new Error(data.responseDetails || "Translation failed")
      }
    } catch (error) {
      console.error("MyMemory translation error:", error)
      throw error
    }
  }

  static async translateWithLibreTranslate(
    text: string,
    fromLang: string,
    toLang: string
  ): Promise<string> {
    try {
      const response = await fetch(this.LIBRETRANSLATE_API, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          q: text,
          source: fromLang === "auto" ? "auto" : fromLang,
          target: toLang,
          format: "text"
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: LibreTranslateResponse = await response.json()
      return data.translatedText
    } catch (error) {
      console.error("LibreTranslate translation error:", error)
      throw error
    }
  }

  static async translate(
    text: string,
    fromLang: string = "auto",
    toLang: string = "vi"
  ): Promise<string> {
    if (!text || text.trim().length === 0) {
      throw new Error("Text is required for translation")
    }

    // Try MyMemory first (more reliable for general translation)
    try {
      return await this.translateWithMyMemory(text, fromLang, toLang)
    } catch (error) {
      console.warn("MyMemory failed, trying LibreTranslate:", error)

      // Fallback to LibreTranslate
      try {
        return await this.translateWithLibreTranslate(text, fromLang, toLang)
      } catch (fallbackError) {
        console.error("All translation services failed:", fallbackError)

        // Last resort: return a mock translation
        return `[${toLang.toUpperCase()}] ${text}`
      }
    }
  }

  static async detectLanguage(text: string): Promise<string> {
    try {
      // Use MyMemory API to detect language
      const url = new URL(this.MYMEMORY_API)
      url.searchParams.append("q", text.substring(0, 100)) // Limit text for detection
      url.searchParams.append("langpair", "auto|en")

      const response = await fetch(url.toString())
      const data: TranslationResponse = await response.json()

      // MyMemory doesn't return detected language directly, so we'll use a simple heuristic
      // This is a limitation of free APIs - paid services like Google Translate provide better language detection

      // Simple language detection based on character patterns
      if (/[\u4e00-\u9fff]/.test(text)) return "zh"
      if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return "ja"
      if (/[\uac00-\ud7af]/.test(text)) return "ko"
      if (
        /[àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ]/.test(
          text
        )
      )
        return "vi"
      if (/[а-яё]/i.test(text)) return "ru"
      if (/[α-ωάέήίόύώ]/i.test(text)) return "el"
      if (/[а-щъьюяєіїґ]/i.test(text)) return "uk"

      // Default to English for Latin scripts
      return "en"
    } catch (error) {
      console.error("Language detection failed:", error)
      return "auto"
    }
  }

  static getSupportedLanguages() {
    return [
      { code: "auto", name: "Tự động phát hiện" },
      { code: "en", name: "English" },
      { code: "vi", name: "Tiếng Việt" },
      { code: "zh", name: "中文 (Chinese)" },
      { code: "ja", name: "日本語 (Japanese)" },
      { code: "ko", name: "한국어 (Korean)" },
      { code: "fr", name: "Français (French)" },
      { code: "de", name: "Deutsch (German)" },
      { code: "es", name: "Español (Spanish)" },
      { code: "ru", name: "Русский (Russian)" },
      { code: "it", name: "Italiano (Italian)" },
      { code: "pt", name: "Português (Portuguese)" },
      { code: "ar", name: "العربية (Arabic)" },
      { code: "hi", name: "हिन्दी (Hindi)" },
      { code: "th", name: "ไทย (Thai)" }
    ]
  }
}

// Helper function for rate limiting
export class RateLimiter {
  private static requests: number[] = []
  private static readonly MAX_REQUESTS_PER_MINUTE = 30

  static async checkRateLimit(): Promise<void> {
    const now = Date.now()
    const oneMinuteAgo = now - 60000

    // Remove old requests
    this.requests = this.requests.filter((time) => time > oneMinuteAgo)

    if (this.requests.length >= this.MAX_REQUESTS_PER_MINUTE) {
      const waitTime = this.requests[0] + 60000 - now
      throw new Error(
        `Rate limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} seconds.`
      )
    }

    this.requests.push(now)
  }
}
