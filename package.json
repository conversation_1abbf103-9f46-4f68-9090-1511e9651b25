{"name": "dubinq", "displayName": "Dubinq", "version": "1.0.0", "description": "A basic Plasmo extension.", "author": "dubinq.com", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"better-auth": "^1.3.14", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "3.4.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "postcss": "8.4.33", "prettier": "3.2.4", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*/*"], "permissions": ["storage", "activeTab"]}}