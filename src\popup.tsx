import { useState } from "react"

import "~style.css"

function IndexPopup() {
  const [sourceText, setSourceText] = useState("")
  const [translatedText, setTranslatedText] = useState("")

  const handleTranslate = async () => {
    if (!sourceText.trim()) return

    // Mock translation for now
    setTranslatedText(`Translation: ${sourceText}`)
  }

  return (
    <div className="plasmo-w-96 plasmo-bg-white plasmo-shadow-xl plasmo-rounded-lg plasmo-overflow-hidden">
      <div className="plasmo-bg-gradient-to-r plasmo-from-blue-500 plasmo-to-purple-600 plasmo-p-4 plasmo-text-white">
        <h1 className="plasmo-text-lg plasmo-font-bold">DubinQ Translator</h1>
      </div>

      <div className="plasmo-p-4 plasmo-space-y-4">
        <textarea
          value={sourceText}
          onChange={(e) => setSourceText(e.target.value)}
          placeholder="Enter text to translate..."
          className="plasmo-w-full plasmo-h-24 plasmo-p-3 plasmo-border plasmo-rounded-lg plasmo-resize-none"
        />

        <button
          onClick={handleTranslate}
          disabled={!sourceText.trim()}
          className="plasmo-w-full plasmo-bg-blue-500 plasmo-text-white plasmo-py-2 plasmo-rounded-lg disabled:plasmo-opacity-50">
          Translate
        </button>

        {translatedText && (
          <div className="plasmo-bg-gray-50 plasmo-rounded-lg plasmo-p-3">
            <div className="plasmo-text-gray-800">{translatedText}</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default IndexPopup
